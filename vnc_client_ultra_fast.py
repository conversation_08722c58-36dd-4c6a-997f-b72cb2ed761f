#!/usr/bin/env python3
"""
超低延迟VNC客户端 - 接近0延时
使用多种技术最大化减少延迟
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import queue
import time
import os
import tempfile
from PIL import Image, ImageTk
from vncdotool import api
import concurrent.futures
import multiprocessing
from datetime import datetime

class VNCClientUltraFast:
    def __init__(self, root):
        self.root = root
        self.root.title("VNC客户端 - 超低延迟版")
        self.root.geometry("1200x800")
        
        # VNC连接相关
        self.vnc_client = None
        self.connected = False
        self.host = ""
        self.port = ""
        self.password = ""
        
        # 超低延迟捕获相关
        self.live_capture = False
        self.capture_threads = []
        self.image_queue = queue.Queue(maxsize=30)  # 支持高帧率的大队列
        self.frame_count = 0
        self.fps_start_time = time.time()
        self.current_fps = 0
        
        # 多连接池 - 更多连接
        self.connection_pools = []
        self.pool_size = 6  # 6个连接
        self.current_pool_index = 0
        
        # 临时文件管理
        self.temp_dir = tempfile.mkdtemp()
        
        # 性能统计
        self.capture_times = []
        self.avg_capture_time = 0
        
        # GUI变量
        self.host_var = tk.StringVar(value="127.0.0.1")
        self.port_var = tk.StringVar(value="5900")
        self.password_var = tk.StringVar(value="")
        self.status_var = tk.StringVar(value="未连接")
        self.fps_var = tk.StringVar(value="FPS: 0")
        self.latency_var = tk.StringVar(value="延迟: 0ms")
        
        self.create_widgets()
        self.start_display_update()
        
        # 程序退出时清理
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_widgets(self):
        """创建GUI组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 连接设置
        conn_frame = ttk.LabelFrame(main_frame, text="VNC连接设置", padding="10")
        conn_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(conn_frame, text="主机:").grid(row=0, column=0, sticky=tk.W)
        ttk.Entry(conn_frame, textvariable=self.host_var, width=15).grid(row=0, column=1, padx=(5, 10))
        
        ttk.Label(conn_frame, text="端口:").grid(row=0, column=2, sticky=tk.W)
        ttk.Entry(conn_frame, textvariable=self.port_var, width=10).grid(row=0, column=3, padx=(5, 10))
        
        ttk.Label(conn_frame, text="密码:").grid(row=0, column=4, sticky=tk.W)
        ttk.Entry(conn_frame, textvariable=self.password_var, show="*", width=15).grid(row=0, column=5, padx=(5, 10))
        
        ttk.Button(conn_frame, text="连接", command=self.connect_vnc).grid(row=0, column=6, padx=(5, 0))
        ttk.Button(conn_frame, text="断开", command=self.disconnect_vnc).grid(row=0, column=7, padx=(5, 0))
        
        # 性能状态显示
        status_frame = ttk.LabelFrame(main_frame, text="实时性能", padding="5")
        status_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(status_frame, text="状态:").grid(row=0, column=0, sticky=tk.W)
        ttk.Label(status_frame, textvariable=self.status_var).grid(row=0, column=1, sticky=tk.W, padx=(5, 20))
        
        ttk.Label(status_frame, textvariable=self.fps_var, foreground="red", font=("Arial", 12, "bold")).grid(row=0, column=2, sticky=tk.W, padx=(0, 20))
        
        ttk.Label(status_frame, textvariable=self.latency_var, foreground="green", font=("Arial", 12, "bold")).grid(row=0, column=3, sticky=tk.W)
        
        # 超低延迟控制
        control_frame = ttk.LabelFrame(main_frame, text="超低延迟控制", padding="5")
        control_frame.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=(0, 10), padx=(10, 0))
        
        self.start_btn = ttk.Button(control_frame, text="🚀 启动超低延迟", command=self.start_ultra_fast_capture, style="Accent.TButton")
        self.start_btn.grid(row=0, column=0, padx=(0, 5))
        
        self.stop_btn = ttk.Button(control_frame, text="⏹ 停止", command=self.stop_live_capture, state="disabled")
        self.stop_btn.grid(row=0, column=1, padx=(0, 5))
        
        ttk.Button(control_frame, text="📸 瞬时截图", command=self.instant_screenshot).grid(row=0, column=2, padx=(0, 5))
        
        # 性能调优
        tune_frame = ttk.LabelFrame(main_frame, text="性能调优", padding="5")
        tune_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(tune_frame, text="并发线程:").grid(row=0, column=0, sticky=tk.W)
        self.thread_count_var = tk.StringVar(value="8")
        thread_combo = ttk.Combobox(tune_frame, textvariable=self.thread_count_var, values=["4", "8", "12", "16"], width=5)
        thread_combo.grid(row=0, column=1, padx=(5, 20))
        
        ttk.Label(tune_frame, text="连接池大小:").grid(row=0, column=2, sticky=tk.W)
        self.pool_size_var = tk.StringVar(value="10")
        pool_combo = ttk.Combobox(tune_frame, textvariable=self.pool_size_var, values=["6", "10", "15", "20"], width=5)
        pool_combo.grid(row=0, column=3, padx=(5, 20))
        
        ttk.Label(tune_frame, text="目标FPS:").grid(row=0, column=4, sticky=tk.W)
        self.fps_target_var = tk.StringVar(value="60")
        fps_combo = ttk.Combobox(tune_frame, textvariable=self.fps_target_var, values=["30", "60", "90", "120", "144"], width=5)
        fps_combo.grid(row=0, column=5, padx=(5, 0))
        
        # 图像显示区域
        self.image_frame = ttk.Frame(main_frame, relief="sunken", borderwidth=2)
        self.image_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.image_label = tk.Label(self.image_frame, text="🔌 未连接到VNC服务器\n点击连接开始超低延迟监控", 
                                   bg="gray90", fg="gray50", font=("Arial", 14))
        self.image_label.pack(expand=True, fill="both")
        
        # 性能日志
        log_frame = ttk.LabelFrame(main_frame, text="性能监控", padding="5")
        log_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        
        self.log_text = tk.Text(log_frame, height=5, wrap=tk.WORD, font=("Consolas", 9))
        scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)
        main_frame.rowconfigure(4, weight=1)
        self.image_frame.columnconfigure(0, weight=1)
        self.image_frame.rowconfigure(0, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
    
    def log_message(self, message):
        """添加性能日志"""
        now = datetime.now()
        timestamp = now.strftime("%H:%M:%S") + f".{now.microsecond//1000:03d}"
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        # 限制日志行数
        lines = self.log_text.get("1.0", tk.END).split('\n')
        if len(lines) > 50:
            self.log_text.delete("1.0", "10.0")
        self.root.update_idletasks()
    
    def connect_vnc(self):
        """连接VNC服务器"""
        if self.connected:
            messagebox.showwarning("警告", "已经连接到VNC服务器")
            return
        
        self.host = self.host_var.get().strip()
        self.port = self.port_var.get().strip()
        self.password = self.password_var.get() if self.password_var.get() else None
        
        if not self.host:
            messagebox.showerror("错误", "请输入主机地址")
            return
        
        try:
            self.log_message(f"🔗 连接到 {self.host}:{self.port}...")
            
            # 构建连接字符串
            connect_string = f"{self.host}::{self.port}"
            
            self.vnc_client = api.connect(connect_string, password=self.password)
            self.connected = True
            self.status_var.set("✅ 已连接")
            self.log_message("✅ VNC连接成功!")
            
            # 初始化超大连接池
            self.initialize_ultra_connection_pool()
            
        except Exception as e:
            error_msg = f"❌ 连接失败: {e}"
            self.log_message(error_msg)
            messagebox.showerror("连接错误", error_msg)
    
    def initialize_ultra_connection_pool(self):
        """初始化超大连接池"""
        def init_pool():
            try:
                pool_size = int(self.pool_size_var.get())
                connect_string = f"{self.host}::{self.port}"
                
                self.connection_pools = []
                for i in range(pool_size):
                    client = api.connect(connect_string, password=self.password)
                    self.connection_pools.append(client)
                    self.root.after(0, lambda i=i: self.log_message(f"🔗 连接池 {i+1}/{pool_size} 就绪"))
                
                self.root.after(0, lambda: self.log_message(f"🚀 超大连接池初始化完成 ({pool_size}个连接)"))
                self.root.after(0, lambda: self.status_var.set(f"⚡ 就绪 ({pool_size}连接)"))
                
            except Exception as e:
                self.root.after(0, lambda err=str(e): self.log_message(f"❌ 连接池初始化失败: {err}"))
        
        threading.Thread(target=init_pool, daemon=True).start()
    
    def get_next_connection(self):
        """轮询获取下一个连接"""
        if not self.connection_pools:
            connect_string = f"{self.host}::{self.port}"
            return api.connect(connect_string, password=self.password)
        
        client = self.connection_pools[self.current_pool_index]
        self.current_pool_index = (self.current_pool_index + 1) % len(self.connection_pools)
        return client
    
    def start_ultra_fast_capture(self):
        """启动超低延迟捕获"""
        if not self.connected:
            messagebox.showwarning("警告", "请先连接到VNC服务器")
            return

        if self.live_capture:
            return

        # 检查连接池状态
        if not self.connection_pools:
            self.log_message("⚠️ 连接池未就绪，等待初始化...")
            self.root.after(1000, self.start_ultra_fast_capture)  # 1秒后重试
            return
        
        self.live_capture = True
        self.frame_count = 0
        self.fps_start_time = time.time()
        self.capture_times = []
        
        self.start_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        
        # 启动多个并发捕获线程
        thread_count = int(self.thread_count_var.get())
        self.capture_threads = []
        
        for i in range(thread_count):
            thread = threading.Thread(target=self.ultra_fast_capture_worker, args=(i,), daemon=True)
            thread.start()
            self.capture_threads.append(thread)
        
        self.log_message(f"🚀 启动超低延迟捕获 ({thread_count}线程)")
    
    def ultra_fast_capture_worker(self, worker_id):
        """超低延迟捕获工作线程"""
        target_fps = int(self.fps_target_var.get())
        thread_count = int(self.thread_count_var.get())
        # 每个线程的目标FPS = 总FPS / 线程数，但最小间隔不超过10ms
        thread_fps = max(target_fps / thread_count, 10)
        sleep_time = 1.0 / thread_fps
        
        # 添加调试信息
        self.root.after(0, lambda wid=worker_id: self.log_message(f"🔧 线程{wid}开始工作 (FPS:{thread_fps:.1f})"))

        while self.live_capture and self.connected:
            start_time = time.time()

            try:
                client = self.get_next_connection()
                temp_filename = os.path.join(self.temp_dir, f"ultra_{worker_id}_{int(time.time() * 1000000)}.png")
                
                # 超快捕获
                capture_start = time.time()
                client.captureScreen(temp_filename)
                capture_time = (time.time() - capture_start) * 1000  # 毫秒
                
                if os.path.exists(temp_filename):
                    # 记录捕获时间
                    self.capture_times.append(capture_time)
                    if len(self.capture_times) > 10:
                        self.capture_times.pop(0)
                    
                    avg_time = sum(self.capture_times) / len(self.capture_times)
                    self.root.after(0, lambda t=avg_time: self.latency_var.set(f"延迟: {t:.1f}ms"))
                    
                    # 将图像放入队列
                    if not self.image_queue.full():
                        self.image_queue.put(temp_filename)
                    else:
                        try:
                            old_file = self.image_queue.get_nowait()
                            if os.path.exists(old_file):
                                os.remove(old_file)
                        except queue.Empty:
                            pass
                        self.image_queue.put(temp_filename)
                    
                    self.frame_count += 1
                    
                    # 计算FPS
                    current_time = time.time()
                    if current_time - self.fps_start_time >= 1.0:
                        fps = self.frame_count / (current_time - self.fps_start_time)
                        self.root.after(0, lambda f=fps: self.fps_var.set(f"FPS: {f:.1f}"))
                        self.frame_count = 0
                        self.fps_start_time = current_time
                
            except Exception as e:
                self.root.after(0, lambda err=str(e), wid=worker_id: self.log_message(f"⚠ 线程{wid}错误: {err}"))
            
            # 精确控制帧率
            elapsed = time.time() - start_time
            if elapsed < sleep_time:
                time.sleep(sleep_time - elapsed)
    
    def stop_live_capture(self):
        """停止实时捕获"""
        if not self.live_capture:
            return
        
        self.live_capture = False
        
        self.start_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        
        # 等待所有线程结束
        for thread in self.capture_threads:
            if thread.is_alive():
                thread.join(timeout=1)
        
        self.fps_var.set("FPS: 0")
        self.latency_var.set("延迟: 0ms")
        self.log_message("⏹ 停止超低延迟捕获")
    
    def instant_screenshot(self):
        """瞬时截图"""
        if not self.connected:
            messagebox.showwarning("警告", "请先连接到VNC服务器")
            return
        
        try:
            start_time = time.time()
            client = self.get_next_connection()
            temp_filename = os.path.join(self.temp_dir, f"instant_{int(time.time() * 1000000)}.png")
            
            client.captureScreen(temp_filename)
            capture_time = (time.time() - start_time) * 1000
            
            if os.path.exists(temp_filename):
                self.image_queue.put(temp_filename)
                self.log_message(f"📸 瞬时截图完成 ({capture_time:.1f}ms)")
            else:
                self.log_message("❌ 瞬时截图失败")
                
        except Exception as e:
            self.log_message(f"❌ 截图错误: {e}")
    
    def display_image(self, image_path):
        """显示图像"""
        try:
            if os.path.exists(image_path):
                image = Image.open(image_path)
                
                # 快速缩放
                self.image_frame.update_idletasks()
                frame_width = self.image_frame.winfo_width()
                frame_height = self.image_frame.winfo_height()
                
                if frame_width > 1 and frame_height > 1:
                    image.thumbnail((frame_width - 10, frame_height - 10), Image.Resampling.NEAREST)  # 最快缩放
                
                photo = ImageTk.PhotoImage(image)
                self.image_label.config(image=photo, text="")
                self.image_label.image = photo
                
        except Exception as e:
            self.log_message(f"❌ 显示错误: {e}")
    
    def start_display_update(self):
        """启动显示更新循环"""
        self.update_display()
    
    def update_display(self):
        """更新显示 - 最高频率"""
        try:
            if not self.image_queue.empty():
                image_path = self.image_queue.get_nowait()
                self.display_image(image_path)
        except queue.Empty:
            pass
        
        # 8ms刷新 = 120FPS显示，支持高帧率
        self.root.after(8, self.update_display)
    
    def disconnect_vnc(self):
        """断开VNC连接"""
        if not self.connected:
            return
        
        # 停止实时捕获
        if self.live_capture:
            self.stop_live_capture()
        
        try:
            # 清理连接池
            for client in self.connection_pools:
                try:
                    client.disconnect()
                except:
                    pass
            self.connection_pools = []
            
            if self.vnc_client:
                self.vnc_client.disconnect()
            
            self.connected = False
            self.vnc_client = None
            self.status_var.set("未连接")
            self.log_message("🔌 VNC连接已断开")
            
        except Exception as e:
            self.log_message(f"❌ 断开错误: {e}")
    
    def on_closing(self):
        """程序关闭时的清理"""
        self.stop_live_capture()
        self.disconnect_vnc()
        
        # 清理临时文件
        try:
            import shutil
            shutil.rmtree(self.temp_dir, ignore_errors=True)
        except:
            pass
        
        self.root.destroy()

def main():
    root = tk.Tk()
    app = VNCClientUltraFast(root)
    root.mainloop()

if __name__ == "__main__":
    main()
