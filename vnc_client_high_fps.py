#!/usr/bin/env python3
"""
高FPS VNC客户端 - 专门优化高帧率性能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import queue
import time
import os
import tempfile
from PIL import Image, ImageTk
from vncdotool import api

class VNCClientHighFPS:
    def __init__(self, root):
        self.root = root
        self.root.title("VNC客户端 - 高FPS优化版")
        self.root.geometry("1000x700")
        
        # VNC连接相关
        self.vnc_client = None
        self.connected = False
        self.host = ""
        self.port = ""
        self.password = ""
        
        # 高FPS捕获相关
        self.live_capture = False
        self.capture_threads = []
        self.image_queue = queue.Queue(maxsize=50)  # 大队列支持高FPS
        self.frame_count = 0
        self.fps_start_time = time.time()
        
        # 临时文件管理
        self.temp_dir = tempfile.mkdtemp()
        
        # GUI变量
        self.host_var = tk.StringVar(value="127.0.0.1")
        self.port_var = tk.StringVar(value="5900")
        self.password_var = tk.StringVar(value="")
        self.status_var = tk.StringVar(value="未连接")
        self.fps_var = tk.StringVar(value="FPS: 0")
        
        self.create_widgets()
        self.start_display_update()
        
        # 程序退出时清理
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_widgets(self):
        """创建GUI组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 连接设置
        conn_frame = ttk.LabelFrame(main_frame, text="VNC连接设置", padding="10")
        conn_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(conn_frame, text="主机:").grid(row=0, column=0, sticky=tk.W)
        ttk.Entry(conn_frame, textvariable=self.host_var, width=15).grid(row=0, column=1, padx=(5, 10))
        
        ttk.Label(conn_frame, text="端口:").grid(row=0, column=2, sticky=tk.W)
        ttk.Entry(conn_frame, textvariable=self.port_var, width=10).grid(row=0, column=3, padx=(5, 10))
        
        ttk.Label(conn_frame, text="密码:").grid(row=0, column=4, sticky=tk.W)
        ttk.Entry(conn_frame, textvariable=self.password_var, show="*", width=15).grid(row=0, column=5, padx=(5, 10))
        
        self.connect_btn = ttk.Button(conn_frame, text="连接", command=self.connect_vnc)
        self.connect_btn.grid(row=0, column=6, padx=(5, 0))
        
        self.disconnect_btn = ttk.Button(conn_frame, text="断开", command=self.disconnect_vnc)
        self.disconnect_btn.grid(row=0, column=7, padx=(5, 0))
        
        # 高FPS设置
        fps_frame = ttk.LabelFrame(main_frame, text="高FPS设置", padding="10")
        fps_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(fps_frame, text="线程数:").grid(row=0, column=0, sticky=tk.W)
        self.thread_count_var = tk.StringVar(value="16")
        thread_combo = ttk.Combobox(fps_frame, textvariable=self.thread_count_var, values=["8", "16", "24", "32"], width=5)
        thread_combo.grid(row=0, column=1, padx=(5, 20))
        
        ttk.Label(fps_frame, text="目标FPS:").grid(row=0, column=2, sticky=tk.W)
        self.fps_target_var = tk.StringVar(value="60")
        fps_combo = ttk.Combobox(fps_frame, textvariable=self.fps_target_var, values=["30", "60", "90", "120", "144", "200"], width=5)
        fps_combo.grid(row=0, column=3, padx=(5, 20))
        
        # 状态和控制
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(control_frame, text="状态:").grid(row=0, column=0, sticky=tk.W)
        self.status_label = ttk.Label(control_frame, textvariable=self.status_var)
        self.status_label.grid(row=0, column=1, sticky=tk.W, padx=(5, 20))
        
        ttk.Label(control_frame, textvariable=self.fps_var, foreground="red", font=("Arial", 14, "bold")).grid(row=0, column=2, sticky=tk.W, padx=(20, 0))
        
        self.start_btn = ttk.Button(control_frame, text="开始高FPS监控", command=self.start_high_fps_capture)
        self.start_btn.grid(row=0, column=3, padx=(20, 5))
        
        self.stop_btn = ttk.Button(control_frame, text="停止监控", command=self.stop_high_fps_capture, state="disabled")
        self.stop_btn.grid(row=0, column=4, padx=(0, 5))
        
        # 图像显示区域
        self.image_frame = ttk.Frame(main_frame, relief="sunken", borderwidth=2)
        self.image_frame.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.image_label = tk.Label(self.image_frame, text="未连接到VNC服务器", 
                                   bg="gray90", fg="gray50", font=("Arial", 12))
        self.image_label.pack(expand=True, fill="both")
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="性能日志", padding="5")
        log_frame.grid(row=4, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        
        self.log_text = tk.Text(log_frame, height=4, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(3, weight=1)
        main_frame.rowconfigure(4, weight=1)
        self.image_frame.columnconfigure(0, weight=1)
        self.image_frame.rowconfigure(0, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
    
    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        # 限制日志行数
        lines = self.log_text.get("1.0", tk.END).split('\n')
        if len(lines) > 50:
            self.log_text.delete("1.0", "10.0")
        self.root.update_idletasks()
    
    def connect_vnc(self):
        """连接VNC服务器"""
        if self.connected:
            messagebox.showwarning("警告", "已经连接到VNC服务器")
            return
        
        self.host = self.host_var.get().strip()
        self.port = self.port_var.get().strip()
        self.password = self.password_var.get() if self.password_var.get() else None
        
        if not self.host:
            messagebox.showerror("错误", "请输入主机地址")
            return
        
        self.connect_btn.config(state="disabled")
        self.status_var.set("连接中...")
        
        def connect_thread():
            try:
                self.log_message(f"正在连接到 {self.host}:{self.port}...")
                
                connect_string = f"{self.host}::{self.port}"
                self.vnc_client = api.connect(connect_string, password=self.password)
                self.connected = True
                
                self.root.after(0, self.on_connect_success)
                
            except Exception as e:
                error_msg = str(e)
                self.root.after(0, lambda: self.on_connect_error(error_msg))
        
        threading.Thread(target=connect_thread, daemon=True).start()
    
    def on_connect_success(self):
        """连接成功回调"""
        self.status_var.set("已连接")
        self.connect_btn.config(state="normal")
        self.log_message("VNC连接成功! 高FPS模式就绪")
    
    def on_connect_error(self, error_msg):
        """连接错误回调"""
        self.status_var.set("连接失败")
        self.connect_btn.config(state="normal")
        self.log_message(f"连接失败: {error_msg}")
        messagebox.showerror("连接错误", f"连接失败: {error_msg}")
    
    def disconnect_vnc(self):
        """断开VNC连接"""
        if not self.connected:
            return
        
        if self.live_capture:
            self.stop_high_fps_capture()
        
        try:
            if self.vnc_client:
                self.vnc_client.disconnect()
            
            self.connected = False
            self.vnc_client = None
            self.status_var.set("未连接")
            self.log_message("VNC连接已断开")
            
        except Exception as e:
            self.log_message(f"断开连接时出错: {e}")
    
    def create_fast_connection(self):
        """创建快速连接"""
        connect_string = f"{self.host}::{self.port}"
        return api.connect(connect_string, password=self.password)
    
    def start_high_fps_capture(self):
        """启动高FPS捕获"""
        if not self.connected:
            messagebox.showwarning("警告", "请先连接到VNC服务器")
            return
        
        if self.live_capture:
            return
        
        self.live_capture = True
        self.frame_count = 0
        self.fps_start_time = time.time()
        
        self.start_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        
        # 启动多个高速捕获线程
        thread_count = int(self.thread_count_var.get())
        target_fps = int(self.fps_target_var.get())
        
        self.capture_threads = []
        for i in range(thread_count):
            thread = threading.Thread(target=self.high_fps_worker, args=(i,), daemon=True)
            thread.start()
            self.capture_threads.append(thread)
        
        self.log_message(f"🚀 启动高FPS捕获: {thread_count}线程, 目标{target_fps}FPS")
    
    def high_fps_worker(self, worker_id):
        """高FPS工作线程 - 极简设计"""
        target_fps = int(self.fps_target_var.get())
        # 极简间隔计算
        sleep_time = max(0.001, 1.0 / target_fps)  # 最小1ms
        
        while self.live_capture and self.connected:
            try:
                # 极速连接和捕获
                client = self.create_fast_connection()
                temp_filename = os.path.join(self.temp_dir, f"fps_{worker_id}_{int(time.time() * 1000000)}.png")
                
                # 最小化捕获
                client.captureScreen(temp_filename)
                client.disconnect()
                
                # 快速入队
                if os.path.exists(temp_filename):
                    if not self.image_queue.full():
                        self.image_queue.put(temp_filename)
                    else:
                        # 快速丢弃
                        try:
                            old_file = self.image_queue.get_nowait()
                            os.remove(old_file)
                        except:
                            pass
                        self.image_queue.put(temp_filename)
                    
                    # 简化FPS计算
                    self.frame_count += 1
                    current_time = time.time()
                    if current_time - self.fps_start_time >= 0.2:  # 每0.2秒更新
                        fps = self.frame_count / (current_time - self.fps_start_time)
                        self.root.after(0, lambda f=fps: self.fps_var.set(f"FPS: {f:.0f}"))
                        self.frame_count = 0
                        self.fps_start_time = current_time
                
                # 极简延迟
                time.sleep(sleep_time)
                
            except:
                # 忽略错误，继续运行
                time.sleep(0.001)
    
    def stop_high_fps_capture(self):
        """停止高FPS捕获"""
        if not self.live_capture:
            return
        
        self.live_capture = False
        
        self.start_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        
        # 等待线程结束
        for thread in self.capture_threads:
            if thread.is_alive():
                thread.join(timeout=1)
        
        self.fps_var.set("FPS: 0")
        self.log_message("停止高FPS监控")
    
    def display_image(self, image_path):
        """显示图像"""
        try:
            if os.path.exists(image_path):
                image = Image.open(image_path)
                
                # 快速缩放
                self.image_frame.update_idletasks()
                frame_width = self.image_frame.winfo_width()
                frame_height = self.image_frame.winfo_height()
                
                if frame_width > 1 and frame_height > 1:
                    image.thumbnail((frame_width - 10, frame_height - 10), Image.Resampling.NEAREST)  # 使用最快的缩放
                
                photo = ImageTk.PhotoImage(image)
                self.image_label.config(image=photo, text="")
                self.image_label.image = photo
                
        except:
            pass  # 忽略显示错误
    
    def start_display_update(self):
        """启动显示更新循环"""
        self.update_display()
    
    def update_display(self):
        """更新显示 - 高频率"""
        try:
            if not self.image_queue.empty():
                image_path = self.image_queue.get_nowait()
                self.display_image(image_path)
        except:
            pass
        
        # 5ms刷新 = 200FPS显示
        self.root.after(5, self.update_display)
    
    def on_closing(self):
        """程序关闭时的清理"""
        self.stop_high_fps_capture()
        self.disconnect_vnc()
        
        # 清理临时文件
        try:
            import shutil
            shutil.rmtree(self.temp_dir, ignore_errors=True)
        except:
            pass
        
        self.root.destroy()

def main():
    root = tk.Tk()
    app = VNCClientHighFPS(root)
    root.mainloop()

if __name__ == "__main__":
    main()
