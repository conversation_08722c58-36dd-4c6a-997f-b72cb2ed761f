#!/usr/bin/env python3
"""
简单的VNC测试程序
"""

import os
import time
import tempfile
from vncdotool import api

def main():
    print("简单VNC测试")
    print("=" * 30)

    # 尝试连接到本地VNC服务器
    connections_to_try = [
        "127.0.0.1:0",      # 显示 :0
        "localhost:0",      # 显示 :0
        "127.0.0.1::5900",  # 端口 5900
        "localhost::5900",  # 端口 5900
    ]

    # 常见的VNC密码
    passwords_to_try = [
        None,           # 无密码
        "",             # 空密码
        "123456",       # 常见密码
        "password",     # 常见密码
        "vnc",          # 常见密码
        "admin",        # 常见密码
        "123",          # 简单密码
    ]

    for conn_str in connections_to_try:
        print(f"\n尝试连接: {conn_str}")

        for password in passwords_to_try:
            try:
                password_desc = "无密码" if password is None else f"密码: '{password}'"
                print(f"  尝试 {password_desc}...")

                client = api.connect(conn_str, password=password)
                print("  ✓ 连接成功!")

                # 尝试截图
                temp_dir = tempfile.mkdtemp()
                screenshot_path = os.path.join(temp_dir, "test.png")

                print("  尝试截图...")
                client.captureScreen(screenshot_path)

                if os.path.exists(screenshot_path):
                    size = os.path.getsize(screenshot_path)
                    print(f"  ✓ 截图成功! 大小: {size} 字节")
                    print(f"    文件: {screenshot_path}")

                    # 尝试第二次截图
                    print("  尝试第二次截图...")
                    screenshot_path2 = os.path.join(temp_dir, "test2.png")
                    client.captureScreen(screenshot_path2)

                    if os.path.exists(screenshot_path2):
                        size2 = os.path.getsize(screenshot_path2)
                        print(f"  ✓ 第二次截图成功! 大小: {size2} 字节")
                    else:
                        print("  ✗ 第二次截图失败")

                else:
                    print("  ✗ 截图失败")

                client.disconnect()
                print("  ✓ 连接已断开")

                # 如果成功，就不再尝试其他连接
                print(f"\n✓ 成功! 连接: {conn_str}, 密码: {password_desc}")
                return

            except Exception as e:
                if "password required" in str(e):
                    print(f"  ✗ 需要密码")
                elif "Authentication failure" in str(e) or "authentication failed" in str(e).lower():
                    print(f"  ✗ 密码错误")
                else:
                    print(f"  ✗ 失败: {e}")
                continue

        print(f"  所有密码都失败了")

    print("\n所有连接尝试都失败了")
    print("请确保VNC服务器正在运行并检查密码")

if __name__ == "__main__":
    main()
