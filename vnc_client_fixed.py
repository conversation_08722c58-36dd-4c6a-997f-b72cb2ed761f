#!/usr/bin/env python3
"""
修复版VNC客户端 - 解决captureScreen阻塞问题
使用超时和重新连接机制
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import queue
import time
import os
import tempfile
from PIL import Image, ImageTk
from vncdotool import api
import signal
import subprocess
import sys

class VNCClientFixed:
    def __init__(self, root):
        self.root = root
        self.root.title("VNC客户端 - 修复版")
        self.root.geometry("1000x700")
        
        # VNC连接相关
        self.vnc_client = None
        self.connected = False
        self.host = ""
        self.port = ""
        self.password = ""
        
        # 实时捕获相关
        self.live_capture = False
        self.capture_thread = None
        self.image_queue = queue.Queue(maxsize=3)
        self.frame_count = 0
        self.fps_start_time = time.time()
        self.current_fps = 0
        
        # 临时文件管理
        self.temp_dir = tempfile.mkdtemp()
        
        # GUI变量
        self.host_var = tk.StringVar(value="127.0.0.1")
        self.port_var = tk.StringVar(value="0")
        self.password_var = tk.StringVar(value="")
        self.status_var = tk.StringVar(value="未连接")
        self.fps_var = tk.StringVar(value="FPS: 0")
        
        self.create_widgets()
        self.start_display_update()
        
        # 程序退出时清理
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_widgets(self):
        """创建GUI组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 连接设置
        conn_frame = ttk.LabelFrame(main_frame, text="VNC连接设置", padding="10")
        conn_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(conn_frame, text="主机:").grid(row=0, column=0, sticky=tk.W)
        ttk.Entry(conn_frame, textvariable=self.host_var, width=15).grid(row=0, column=1, padx=(5, 10))
        
        ttk.Label(conn_frame, text="端口/显示:").grid(row=0, column=2, sticky=tk.W)
        ttk.Entry(conn_frame, textvariable=self.port_var, width=10).grid(row=0, column=3, padx=(5, 10))
        
        ttk.Label(conn_frame, text="密码:").grid(row=0, column=4, sticky=tk.W)
        ttk.Entry(conn_frame, textvariable=self.password_var, show="*", width=15).grid(row=0, column=5, padx=(5, 10))
        
        ttk.Button(conn_frame, text="连接", command=self.connect_vnc).grid(row=0, column=6, padx=(5, 0))
        ttk.Button(conn_frame, text="断开", command=self.disconnect_vnc).grid(row=0, column=7, padx=(5, 0))
        
        # 状态显示
        status_frame = ttk.Frame(main_frame)
        status_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(status_frame, text="状态:").grid(row=0, column=0, sticky=tk.W)
        ttk.Label(status_frame, textvariable=self.status_var).grid(row=0, column=1, sticky=tk.W, padx=(5, 20))
        
        ttk.Label(status_frame, textvariable=self.fps_var).grid(row=0, column=2, sticky=tk.W)
        
        # 控制按钮
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.start_btn = ttk.Button(control_frame, text="开始实时监控", command=self.start_live_capture)
        self.start_btn.grid(row=0, column=0, padx=(0, 5))
        
        self.stop_btn = ttk.Button(control_frame, text="停止监控", command=self.stop_live_capture, state="disabled")
        self.stop_btn.grid(row=0, column=1, padx=(0, 5))
        
        ttk.Button(control_frame, text="单次截图", command=self.take_single_screenshot).grid(row=0, column=2, padx=(0, 5))
        
        # 图像显示区域
        self.image_frame = ttk.Frame(main_frame, relief="sunken", borderwidth=2)
        self.image_frame.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.image_label = tk.Label(self.image_frame, text="未连接到VNC服务器", 
                                   bg="gray90", fg="gray50", font=("Arial", 12))
        self.image_label.pack(expand=True, fill="both")
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="日志", padding="5")
        log_frame.grid(row=4, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        
        self.log_text = tk.Text(log_frame, height=8, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(3, weight=1)
        main_frame.rowconfigure(4, weight=1)
        self.image_frame.columnconfigure(0, weight=1)
        self.image_frame.rowconfigure(0, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
    
    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def connect_vnc(self):
        """连接VNC服务器"""
        if self.connected:
            messagebox.showwarning("警告", "已经连接到VNC服务器")
            return
        
        self.host = self.host_var.get().strip()
        self.port = self.port_var.get().strip()
        self.password = self.password_var.get() if self.password_var.get() else None
        
        if not self.host:
            messagebox.showerror("错误", "请输入主机地址")
            return
        
        try:
            self.log_message(f"正在连接到 {self.host}:{self.port}...")
            
            # 构建连接字符串
            if self.port and self.port != "0":
                if ":" in self.port:  # 如果端口包含冒号，说明是完整端口号
                    connect_string = f"{self.host}::{self.port}"
                else:
                    connect_string = f"{self.host}:{self.port}"
            else:
                connect_string = f"{self.host}:0"
            
            self.vnc_client = api.connect(connect_string, password=self.password)
            self.connected = True
            self.status_var.set("已连接")
            self.log_message("VNC连接成功!")
            
        except Exception as e:
            error_msg = f"连接失败: {e}"
            self.log_message(error_msg)
            messagebox.showerror("连接错误", error_msg)
    
    def disconnect_vnc(self):
        """断开VNC连接"""
        if not self.connected:
            return
        
        # 停止实时捕获
        if self.live_capture:
            self.stop_live_capture()
        
        try:
            if self.vnc_client:
                self.vnc_client.disconnect()
            self.connected = False
            self.vnc_client = None
            self.status_var.set("未连接")
            self.log_message("VNC连接已断开")
            
        except Exception as e:
            self.log_message(f"断开连接时出错: {e}")
    
    def start_live_capture(self):
        """开始实时捕获"""
        if not self.connected:
            messagebox.showwarning("警告", "请先连接到VNC服务器")
            return
        
        if self.live_capture:
            return
        
        self.live_capture = True
        self.frame_count = 0
        self.fps_start_time = time.time()
        
        self.start_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        
        # 启动捕获线程
        self.capture_thread = threading.Thread(target=self.capture_loop_with_timeout, daemon=True)
        self.capture_thread.start()
        
        self.log_message("开始实时屏幕监控")
    
    def stop_live_capture(self):
        """停止实时捕获"""
        if not self.live_capture:
            return
        
        self.live_capture = False
        
        self.start_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        
        # 等待线程结束
        if self.capture_thread and self.capture_thread.is_alive():
            self.capture_thread.join(timeout=2)
        
        self.fps_var.set("FPS: 0")
        self.log_message("停止实时屏幕监控")
    
    def capture_loop_with_timeout(self):
        """带超时的捕获循环"""
        capture_count = 0
        
        while self.live_capture and self.connected:
            capture_count += 1
            self.root.after(0, lambda count=capture_count: self.log_message(f"第 {count} 次捕获..."))
            
            success = self.capture_screen_with_timeout()
            
            if success:
                self.frame_count += 1
                current_time = time.time()
                if current_time - self.fps_start_time >= 1.0:
                    fps = self.frame_count / (current_time - self.fps_start_time)
                    self.root.after(0, lambda f=fps: self.fps_var.set(f"FPS: {f:.1f}"))
                    self.frame_count = 0
                    self.fps_start_time = current_time
            
            # 等待下次捕获
            time.sleep(2.0)  # 0.5 FPS
    
    def capture_screen_with_timeout(self):
        """带超时的屏幕捕获"""
        try:
            # 创建新的VNC连接进行捕获
            temp_client = api.connect(f"{self.host}:{self.port}", password=self.password)
            temp_client.timeout = 5  # 设置5秒超时
            
            temp_filename = os.path.join(self.temp_dir, f"frame_{int(time.time() * 1000000)}.png")
            
            temp_client.captureScreen(temp_filename)
            temp_client.disconnect()
            
            if os.path.exists(temp_filename):
                # 将图像放入队列
                if not self.image_queue.full():
                    self.image_queue.put(temp_filename)
                else:
                    try:
                        old_file = self.image_queue.get_nowait()
                        if os.path.exists(old_file):
                            os.remove(old_file)
                    except queue.Empty:
                        pass
                    self.image_queue.put(temp_filename)
                
                self.root.after(0, lambda: self.log_message("捕获成功"))
                return True
            else:
                self.root.after(0, lambda: self.log_message("捕获失败: 文件未生成"))
                return False
                
        except Exception as e:
            self.root.after(0, lambda err=str(e): self.log_message(f"捕获错误: {err}"))
            return False
    
    def take_single_screenshot(self):
        """单次截图"""
        if not self.connected:
            messagebox.showwarning("警告", "请先连接到VNC服务器")
            return
        
        try:
            self.log_message("开始单次截图...")
            success = self.capture_screen_with_timeout()
            
            if success and not self.image_queue.empty():
                # 显示最新的截图
                image_path = self.image_queue.get_nowait()
                self.display_image(image_path)
                self.log_message("单次截图完成")
            else:
                self.log_message("单次截图失败")
                
        except Exception as e:
            error_msg = f"截图失败: {e}"
            self.log_message(error_msg)
            messagebox.showerror("错误", error_msg)
    
    def display_image(self, image_path):
        """显示图像"""
        try:
            if os.path.exists(image_path):
                image = Image.open(image_path)
                
                # 缩放图像以适应显示区域
                self.image_frame.update_idletasks()
                frame_width = self.image_frame.winfo_width()
                frame_height = self.image_frame.winfo_height()
                
                if frame_width > 1 and frame_height > 1:
                    image.thumbnail((frame_width - 10, frame_height - 10), Image.Resampling.LANCZOS)
                
                photo = ImageTk.PhotoImage(image)
                self.image_label.config(image=photo, text="")
                self.image_label.image = photo  # 保持引用
                
        except Exception as e:
            self.log_message(f"显示图像失败: {e}")
    
    def start_display_update(self):
        """启动显示更新循环"""
        self.update_display()
    
    def update_display(self):
        """更新显示"""
        try:
            if not self.image_queue.empty():
                image_path = self.image_queue.get_nowait()
                self.display_image(image_path)
        except queue.Empty:
            pass
        
        # 每50ms检查一次
        self.root.after(50, self.update_display)
    
    def on_closing(self):
        """程序关闭时的清理"""
        self.stop_live_capture()
        self.disconnect_vnc()
        
        # 清理临时文件
        try:
            import shutil
            shutil.rmtree(self.temp_dir, ignore_errors=True)
        except:
            pass
        
        self.root.destroy()

def main():
    root = tk.Tk()
    app = VNCClientFixed(root)
    root.mainloop()

if __name__ == "__main__":
    main()
