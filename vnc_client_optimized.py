#!/usr/bin/env python3
"""
优化版VNC客户端 - 高性能实时捕获
使用连接池和异步处理提高捕获速度
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import queue
import time
import os
import tempfile
from PIL import Image, ImageTk
from vncdotool import api
import concurrent.futures

class VNCClientOptimized:
    def __init__(self, root):
        self.root = root
        self.root.title("VNC客户端 - 优化版")
        self.root.geometry("1000x700")
        
        # VNC连接相关
        self.vnc_client = None
        self.connected = False
        self.host = ""
        self.port = ""
        self.password = ""
        
        # 高性能捕获相关
        self.live_capture = False
        self.capture_thread = None
        self.image_queue = queue.Queue(maxsize=5)
        self.frame_count = 0
        self.fps_start_time = time.time()
        self.current_fps = 0
        
        # 连接池
        self.connection_pool = queue.Queue(maxsize=3)
        self.pool_initialized = False
        
        # 临时文件管理
        self.temp_dir = tempfile.mkdtemp()
        
        # GUI变量
        self.host_var = tk.StringVar(value="127.0.0.1")
        self.port_var = tk.StringVar(value="5900")
        self.password_var = tk.StringVar(value="")
        self.status_var = tk.StringVar(value="未连接")
        self.fps_var = tk.StringVar(value="FPS: 0")
        
        self.create_widgets()
        self.start_display_update()
        
        # 程序退出时清理
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_widgets(self):
        """创建GUI组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 连接设置
        conn_frame = ttk.LabelFrame(main_frame, text="VNC连接设置", padding="10")
        conn_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(conn_frame, text="主机:").grid(row=0, column=0, sticky=tk.W)
        ttk.Entry(conn_frame, textvariable=self.host_var, width=15).grid(row=0, column=1, padx=(5, 10))
        
        ttk.Label(conn_frame, text="端口:").grid(row=0, column=2, sticky=tk.W)
        ttk.Entry(conn_frame, textvariable=self.port_var, width=10).grid(row=0, column=3, padx=(5, 10))
        
        ttk.Label(conn_frame, text="密码:").grid(row=0, column=4, sticky=tk.W)
        ttk.Entry(conn_frame, textvariable=self.password_var, show="*", width=15).grid(row=0, column=5, padx=(5, 10))
        
        ttk.Button(conn_frame, text="连接", command=self.connect_vnc).grid(row=0, column=6, padx=(5, 0))
        ttk.Button(conn_frame, text="断开", command=self.disconnect_vnc).grid(row=0, column=7, padx=(5, 0))
        
        # 状态显示
        status_frame = ttk.Frame(main_frame)
        status_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(status_frame, text="状态:").grid(row=0, column=0, sticky=tk.W)
        ttk.Label(status_frame, textvariable=self.status_var).grid(row=0, column=1, sticky=tk.W, padx=(5, 20))
        
        ttk.Label(status_frame, textvariable=self.fps_var, foreground="blue", font=("Arial", 10, "bold")).grid(row=0, column=2, sticky=tk.W)
        
        # 控制按钮
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.start_btn = ttk.Button(control_frame, text="开始高速监控", command=self.start_live_capture)
        self.start_btn.grid(row=0, column=0, padx=(0, 5))
        
        self.stop_btn = ttk.Button(control_frame, text="停止监控", command=self.stop_live_capture, state="disabled")
        self.stop_btn.grid(row=0, column=1, padx=(0, 5))
        
        ttk.Button(control_frame, text="单次截图", command=self.take_single_screenshot).grid(row=0, column=2, padx=(0, 5))
        
        # 性能设置
        perf_frame = ttk.LabelFrame(main_frame, text="性能设置", padding="5")
        perf_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10), padx=(300, 0))
        
        ttk.Label(perf_frame, text="目标FPS:").grid(row=0, column=0, sticky=tk.W)
        self.fps_target_var = tk.StringVar(value="10")
        fps_combo = ttk.Combobox(perf_frame, textvariable=self.fps_target_var, values=["5", "10", "15", "20"], width=5)
        fps_combo.grid(row=0, column=1, padx=(5, 0))
        fps_combo.bind("<<ComboboxSelected>>", self.update_fps_target)
        
        # 图像显示区域
        self.image_frame = ttk.Frame(main_frame, relief="sunken", borderwidth=2)
        self.image_frame.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.image_label = tk.Label(self.image_frame, text="未连接到VNC服务器", 
                                   bg="gray90", fg="gray50", font=("Arial", 12))
        self.image_label.pack(expand=True, fill="both")
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="性能日志", padding="5")
        log_frame.grid(row=4, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        
        self.log_text = tk.Text(log_frame, height=6, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(3, weight=1)
        main_frame.rowconfigure(4, weight=1)
        self.image_frame.columnconfigure(0, weight=1)
        self.image_frame.rowconfigure(0, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
    
    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        # 限制日志行数
        lines = self.log_text.get("1.0", tk.END).split('\n')
        if len(lines) > 100:
            self.log_text.delete("1.0", "20.0")
        self.root.update_idletasks()
    
    def update_fps_target(self, event=None):
        """更新目标FPS"""
        try:
            target_fps = int(self.fps_target_var.get())
            self.log_message(f"目标FPS设置为: {target_fps}")
        except ValueError:
            pass
    
    def connect_vnc(self):
        """连接VNC服务器"""
        if self.connected:
            messagebox.showwarning("警告", "已经连接到VNC服务器")
            return
        
        self.host = self.host_var.get().strip()
        self.port = self.port_var.get().strip()
        self.password = self.password_var.get() if self.password_var.get() else None
        
        if not self.host:
            messagebox.showerror("错误", "请输入主机地址")
            return
        
        try:
            self.log_message(f"正在连接到 {self.host}:{self.port}...")
            
            # 构建连接字符串
            connect_string = f"{self.host}::{self.port}"
            
            self.vnc_client = api.connect(connect_string, password=self.password)
            self.connected = True
            self.status_var.set("已连接")
            self.log_message("VNC连接成功!")
            
            # 初始化连接池
            self.initialize_connection_pool()
            
        except Exception as e:
            error_msg = f"连接失败: {e}"
            self.log_message(error_msg)
            messagebox.showerror("连接错误", error_msg)
    
    def initialize_connection_pool(self):
        """初始化连接池"""
        if self.pool_initialized:
            return
        
        def init_pool():
            try:
                connect_string = f"{self.host}::{self.port}"
                for i in range(3):  # 创建3个连接
                    client = api.connect(connect_string, password=self.password)
                    self.connection_pool.put(client)
                self.pool_initialized = True
                self.root.after(0, lambda: self.log_message("连接池初始化完成 (3个连接)"))
            except Exception as e:
                self.root.after(0, lambda err=str(e): self.log_message(f"连接池初始化失败: {err}"))
        
        threading.Thread(target=init_pool, daemon=True).start()
    
    def get_connection(self):
        """从连接池获取连接"""
        try:
            return self.connection_pool.get_nowait()
        except queue.Empty:
            # 如果池为空，创建新连接
            connect_string = f"{self.host}::{self.port}"
            return api.connect(connect_string, password=self.password)
    
    def return_connection(self, client):
        """归还连接到池"""
        try:
            if not self.connection_pool.full():
                self.connection_pool.put_nowait(client)
            else:
                client.disconnect()
        except:
            try:
                client.disconnect()
            except:
                pass
    
    def disconnect_vnc(self):
        """断开VNC连接"""
        if not self.connected:
            return
        
        # 停止实时捕获
        if self.live_capture:
            self.stop_live_capture()
        
        try:
            # 清理连接池
            while not self.connection_pool.empty():
                try:
                    client = self.connection_pool.get_nowait()
                    client.disconnect()
                except:
                    pass
            
            if self.vnc_client:
                self.vnc_client.disconnect()
            
            self.connected = False
            self.vnc_client = None
            self.pool_initialized = False
            self.status_var.set("未连接")
            self.log_message("VNC连接已断开")
            
        except Exception as e:
            self.log_message(f"断开连接时出错: {e}")
    
    def start_live_capture(self):
        """开始实时捕获"""
        if not self.connected:
            messagebox.showwarning("警告", "请先连接到VNC服务器")
            return
        
        if self.live_capture:
            return
        
        self.live_capture = True
        self.frame_count = 0
        self.fps_start_time = time.time()
        
        self.start_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        
        # 启动高性能捕获线程
        self.capture_thread = threading.Thread(target=self.high_performance_capture_loop, daemon=True)
        self.capture_thread.start()
        
        self.log_message("开始高性能实时监控")
    
    def stop_live_capture(self):
        """停止实时捕获"""
        if not self.live_capture:
            return
        
        self.live_capture = False
        
        self.start_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        
        # 等待线程结束
        if self.capture_thread and self.capture_thread.is_alive():
            self.capture_thread.join(timeout=2)
        
        self.fps_var.set("FPS: 0")
        self.log_message("停止实时监控")
    
    def high_performance_capture_loop(self):
        """高性能捕获循环"""
        target_fps = int(self.fps_target_var.get())
        sleep_time = 1.0 / target_fps
        
        self.root.after(0, lambda: self.log_message(f"启动高性能捕获 (目标: {target_fps} FPS)"))
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:
            while self.live_capture and self.connected:
                start_time = time.time()
                
                # 异步捕获
                future = executor.submit(self.fast_capture_screen)
                
                try:
                    success = future.result(timeout=1.0)  # 1秒超时
                    if success:
                        self.frame_count += 1
                        
                        # 计算FPS
                        current_time = time.time()
                        if current_time - self.fps_start_time >= 1.0:
                            fps = self.frame_count / (current_time - self.fps_start_time)
                            self.root.after(0, lambda f=fps: self.fps_var.set(f"FPS: {f:.1f}"))
                            self.frame_count = 0
                            self.fps_start_time = current_time
                
                except concurrent.futures.TimeoutError:
                    self.root.after(0, lambda: self.log_message("捕获超时，跳过此帧"))
                
                # 控制帧率
                elapsed = time.time() - start_time
                if elapsed < sleep_time:
                    time.sleep(sleep_time - elapsed)
    
    def fast_capture_screen(self):
        """快速屏幕捕获"""
        client = None
        try:
            client = self.get_connection()
            temp_filename = os.path.join(self.temp_dir, f"frame_{int(time.time() * 1000000)}.png")
            
            client.captureScreen(temp_filename)
            
            if os.path.exists(temp_filename):
                # 将图像放入队列
                if not self.image_queue.full():
                    self.image_queue.put(temp_filename)
                else:
                    try:
                        old_file = self.image_queue.get_nowait()
                        if os.path.exists(old_file):
                            os.remove(old_file)
                    except queue.Empty:
                        pass
                    self.image_queue.put(temp_filename)
                
                return True
            return False
            
        except Exception as e:
            self.root.after(0, lambda err=str(e): self.log_message(f"快速捕获错误: {err}"))
            return False
        finally:
            if client:
                self.return_connection(client)
    
    def take_single_screenshot(self):
        """单次截图"""
        if not self.connected:
            messagebox.showwarning("警告", "请先连接到VNC服务器")
            return
        
        try:
            self.log_message("执行单次截图...")
            success = self.fast_capture_screen()
            
            if success and not self.image_queue.empty():
                self.log_message("单次截图完成")
            else:
                self.log_message("单次截图失败")
                
        except Exception as e:
            error_msg = f"截图失败: {e}"
            self.log_message(error_msg)
            messagebox.showerror("错误", error_msg)
    
    def display_image(self, image_path):
        """显示图像"""
        try:
            if os.path.exists(image_path):
                image = Image.open(image_path)
                
                # 缩放图像以适应显示区域
                self.image_frame.update_idletasks()
                frame_width = self.image_frame.winfo_width()
                frame_height = self.image_frame.winfo_height()
                
                if frame_width > 1 and frame_height > 1:
                    image.thumbnail((frame_width - 10, frame_height - 10), Image.Resampling.LANCZOS)
                
                photo = ImageTk.PhotoImage(image)
                self.image_label.config(image=photo, text="")
                self.image_label.image = photo  # 保持引用
                
        except Exception as e:
            self.log_message(f"显示图像失败: {e}")
    
    def start_display_update(self):
        """启动显示更新循环"""
        self.update_display()
    
    def update_display(self):
        """更新显示"""
        try:
            if not self.image_queue.empty():
                image_path = self.image_queue.get_nowait()
                self.display_image(image_path)
        except queue.Empty:
            pass
        
        # 每30ms检查一次，提高响应性
        self.root.after(30, self.update_display)
    
    def on_closing(self):
        """程序关闭时的清理"""
        self.stop_live_capture()
        self.disconnect_vnc()
        
        # 清理临时文件
        try:
            import shutil
            shutil.rmtree(self.temp_dir, ignore_errors=True)
        except:
            pass
        
        self.root.destroy()

def main():
    root = tk.Tk()
    app = VNCClientOptimized(root)
    root.mainloop()

if __name__ == "__main__":
    main()
