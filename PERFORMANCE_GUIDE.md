# VNC客户端性能优化指南

## 🚀 性能特性

### 实时性优化
- **捕获频率**: 约30 FPS的屏幕捕获
- **显示频率**: 约60 FPS的GUI更新
- **响应延迟**: 通常 < 50ms

### 内存管理
- **队列限制**: 最多缓存3帧图像
- **自动清理**: 及时删除临时文件
- **内存优化**: 智能图像缩放和压缩

### 线程安全
- **多线程架构**: 捕获线程 + GUI线程
- **资源锁定**: 避免截图操作冲突
- **异常处理**: 完善的错误恢复机制

## ⚡ 性能调优

### 1. 网络优化
```python
# 建议的网络设置
- 局域网: 延迟 < 5ms，带宽 > 10Mbps
- 广域网: 延迟 < 100ms，带宽 > 2Mbps
- VPN连接: 使用高性能VPN服务
```

### 2. 系统资源
```
推荐配置:
- CPU: 双核以上
- 内存: 4GB以上
- 网络: 稳定的网络连接
```

### 3. VNC服务器优化
```
服务器端建议:
- 降低颜色深度 (16位或24位)
- 启用压缩算法
- 调整更新频率
- 使用硬件加速
```

## 🔧 故障排除

### 常见问题

#### 1. 实时性不够
**症状**: 画面更新延迟明显
**解决方案**:
- 检查网络连接质量
- 降低VNC服务器分辨率
- 关闭其他网络密集型应用
- 调整捕获频率设置

#### 2. 截图功能无响应
**症状**: 单次截图后程序卡死
**解决方案**:
- 已通过线程锁机制解决
- 确保VNC连接稳定
- 重启程序重新连接

#### 3. 内存使用过高
**症状**: 程序运行时内存持续增长
**解决方案**:
- 队列大小已优化为3帧
- 自动清理临时文件
- 定期重启长时间运行的会话

#### 4. CPU使用率高
**症状**: CPU占用率持续较高
**解决方案**:
- 降低捕获频率 (修改sleep时间)
- 减少图像处理质量
- 使用更高效的图像格式

## 📊 性能监控

### 实时指标
- **FPS显示**: 左上角实时帧率
- **连接状态**: 顶部状态栏
- **操作日志**: 右侧日志区域

### 性能基准
```
良好性能指标:
- 捕获FPS: 20-30
- 显示延迟: < 100ms
- 内存使用: < 200MB
- CPU使用: < 20%
```

## 🛠️ 高级配置

### 自定义捕获频率
```python
# 在 capture_loop 方法中修改
time.sleep(0.033)  # 30 FPS
time.sleep(0.050)  # 20 FPS  
time.sleep(0.100)  # 10 FPS
```

### 调整队列大小
```python
# 在初始化中修改
self.image_queue = queue.Queue(maxsize=3)  # 默认
self.image_queue = queue.Queue(maxsize=5)  # 更多缓存
self.image_queue = queue.Queue(maxsize=1)  # 最低延迟
```

### 图像质量设置
```python
# 在图像缩放中调整
Image.Resampling.LANCZOS  # 高质量 (默认)
Image.Resampling.BILINEAR # 中等质量
Image.Resampling.NEAREST  # 低质量，高性能
```

## 🔍 调试模式

### 启用详细日志
```python
# 在日志方法中添加时间戳
import datetime
timestamp = datetime.datetime.now().strftime("%H:%M:%S.%f")[:-3]
self.log_message(f"[{timestamp}] {message}")
```

### 性能分析
```python
# 添加性能计时
import time
start_time = time.time()
# ... 执行操作 ...
elapsed = time.time() - start_time
print(f"操作耗时: {elapsed:.3f}秒")
```

## 📈 最佳实践

1. **连接前检查**: 确保网络连接稳定
2. **适当分辨率**: 根据网络条件选择合适的分辨率
3. **定期重连**: 长时间使用建议定期重新连接
4. **资源监控**: 关注内存和CPU使用情况
5. **错误处理**: 遇到问题及时查看日志信息

## 🎯 性能目标

- **启动时间**: < 3秒
- **连接时间**: < 5秒
- **截图响应**: < 1秒
- **实时延迟**: < 100ms
- **稳定运行**: > 1小时无故障
