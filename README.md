# VNC客户端连接示例

这个项目包含了两个VNC客户端的实现示例，可以连接到VNC服务器并进行远程桌面操作。

## 文件说明

- `vnc_client.py` - 原生Python实现的VNC客户端（不依赖第三方库）
- `vnc_client_simple.py` - 基于tkinter的VNC客户端图形界面
- `run_vnc_gui.py` - GUI版本的启动脚本（自动检查依赖）
- `requirements.txt` - 项目依赖包列表

## VNC协议简介

VNC (Virtual Network Computing) 是一种远程桌面协议，允许用户通过网络远程控制另一台计算机的桌面。VNC协议的主要特点：

- **跨平台**: 支持Windows、Linux、macOS等多种操作系统
- **网络透明**: 通过TCP/IP网络传输
- **像素级传输**: 传输实际的屏幕像素数据
- **输入转发**: 支持键盘和鼠标事件转发

## 安装依赖

### 方法1: 使用pip安装
```bash
pip install -r requirements.txt
```

### 方法2: 手动安装主要依赖
```bash
pip install vncdotool
```

## 使用方法

### 1. 原生实现版本 (vnc_client.py)

这个版本不依赖第三方库，直接实现了VNC协议的基本功能：

```bash
python vnc_client.py
```

功能特点：
- 支持VNC协议握手
- 支持密码认证
- 可以请求屏幕更新
- 可以发送键盘和鼠标事件
- 纯Python实现，无外部依赖

### 2. GUI图形界面版本 (vnc_client_simple.py)

这个版本提供了基于tkinter的图形用户界面：

```bash
python vnc_client_simple.py
```

或者使用启动脚本（推荐）：

```bash
python run_vnc_gui.py
```

功能特点：
- **实时屏幕监控**: 连续捕获和显示VNC屏幕，支持FPS显示
- **直接交互**: 可以直接在屏幕上点击进行操作
- **多线程架构**: 避免界面冻结，提供流畅体验
- **智能缩放**: 自动调整显示大小，保持宽高比
- **图形化界面**: 基于tkinter的直观GUI，左右分栏布局
- **连接管理**: 可视化的连接状态和配置
- **屏幕操作**: 实时监控、单次截图、保存画面
- **文本输入**: 图形化的文本发送界面
- **鼠标操作**: 可视化的鼠标点击控制（左键/右键/中键）
- **按键发送**: 支持自定义按键和常用快捷键
- **操作日志**: 实时显示操作记录和状态
- **内存优化**: 自动清理临时文件，限制内存使用
- **依赖检测**: 自动检测和安装所需依赖

## 配置说明

运行程序时，您需要提供以下信息：

1. **VNC服务器地址**: 默认为 `localhost`
2. **VNC端口**: 默认为 `5900`
3. **VNC密码**: 可选，如果服务器需要认证

### 常见VNC端口

- 5900: VNC显示器 :0
- 5901: VNC显示器 :1
- 5902: VNC显示器 :2
- 以此类推...

## 快速开始

### 1. 运行测试
```bash
python test_vnc_gui.py
```

### 2. 启动GUI
```bash
python run_vnc_gui.py
```

### 3. 连接和使用
1. 输入VNC服务器信息并点击"连接"
2. 连接成功后，点击"开始实时监控"
3. 在左侧屏幕区域可以直接点击操作
4. 右侧提供各种VNC操作控件

## 使用示例

### 连接到本地VNC服务器
```
VNC服务器地址: localhost
VNC端口: 5900
VNC密码: (留空或输入密码)
```

### 连接到远程VNC服务器
```
VNC服务器地址: *************
VNC端口: 5901
VNC密码: mypassword
```

## 支持的操作

### 原生版本支持的操作：
1. 请求屏幕更新
2. 发送键盘事件（按键按下/释放）
3. 发送鼠标事件（移动和点击）
4. 断开连接

### GUI版本支持的操作：
1. **连接管理**
   - 可视化连接配置（服务器地址、端口、密码）
   - 实时连接状态显示
   - 一键连接/断开

2. **实时屏幕监控**
   - 开始/停止实时屏幕捕获
   - 实时FPS显示
   - 连续画面更新（约10-15 FPS）
   - 直接在屏幕上点击交互

3. **屏幕操作**
   - 单次截图功能
   - 保存当前画面到文件
   - 智能图像缩放显示
   - 支持左键和右键点击

4. **输入控制**
   - 发送文本到远程桌面
   - 鼠标点击控制（支持坐标指定）
   - 自定义按键发送
   - 常用快捷键（Enter、Tab、Escape、Ctrl+C、Ctrl+V、Alt+Tab）

5. **界面布局**
   - 左侧：实时屏幕显示区域
   - 右侧：操作控件和日志
   - 顶部：连接配置和状态

6. **日志监控**
   - 实时操作日志显示
   - 错误信息提示
   - 日志清空功能

## 键码参考

常用键码（用于原生版本）：
- Enter: 65293
- Space: 32
- Backspace: 65288
- Tab: 65289
- Escape: 65307
- F1-F12: 65470-65481
- 字母A-Z: 65-90
- 数字0-9: 48-57

## 注意事项

1. **网络安全**: VNC协议本身不加密，在公网使用时建议通过VPN或SSH隧道
2. **防火墙**: 确保VNC端口在防火墙中已开放
3. **性能**: VNC传输像素数据，网络带宽会影响使用体验
4. **兼容性**: 不同VNC服务器可能有细微差异，如遇问题请检查服务器设置

## 故障排除

### 连接失败
- 检查VNC服务器是否运行
- 检查网络连接和端口
- 确认防火墙设置

### 认证失败
- 检查密码是否正确
- 确认VNC服务器的认证设置

### 依赖安装失败
```bash
# 更新pip
python -m pip install --upgrade pip

# 手动安装依赖
pip install vncdotool Pillow twisted
```

## GUI界面说明

### 主界面布局
- **连接配置区**: 输入VNC服务器信息和连接控制
- **操作控制区**: VNC远程操作功能
- **日志显示区**: 实时操作记录和状态信息

### 使用步骤
1. 输入VNC服务器地址和端口
2. 如需要，输入VNC密码
3. 点击"连接"按钮建立连接
4. 连接成功后可进行各种远程操作
5. 完成后点击"断开"按钮

### 快捷操作
- **Enter键**: 在文本输入框中按Enter可直接发送
- **截图查看**: 双击截图按钮可快速查看
- **常用按键**: 提供常用快捷键的快速按钮

## 扩展功能

您可以基于这些示例扩展更多功能：

- 文件传输功能
- 剪贴板同步
- 多显示器支持
- 操作录制和回放
- 图像压缩优化
- 连接配置保存
- 多连接管理

## 许可证

本项目仅供学习和参考使用。
