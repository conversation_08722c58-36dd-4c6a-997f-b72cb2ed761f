#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的VNC客户端测试程序
用于验证基本的VNC连接和截图功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import os
import tempfile
import queue

try:
    from vncdotool import api
    VNCDOTOOL_AVAILABLE = True
except ImportError:
    VNCDOTOOL_AVAILABLE = False

try:
    from PIL import Image, ImageTk
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

class SimpleVNCTest:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("简化VNC测试")
        self.root.geometry("800x600")
        
        # 状态变量
        self.connected = False
        self.vnc_client = None
        self.live_capture = False
        self.capture_thread = None
        self.frame_count = 0
        self.fps_start_time = time.time()
        self.image_queue = queue.Queue(maxsize=3)
        
        # 临时目录
        self.temp_dir = tempfile.mkdtemp()
        
        self.setup_ui()
        self.check_dependencies()
        
    def setup_ui(self):
        """设置用户界面"""
        # 连接区域
        conn_frame = ttk.Frame(self.root)
        conn_frame.pack(fill="x", padx=10, pady=5)
        
        ttk.Label(conn_frame, text="服务器:").pack(side="left")
        self.host_var = tk.StringVar(value="localhost")
        ttk.Entry(conn_frame, textvariable=self.host_var, width=15).pack(side="left", padx=5)
        
        ttk.Label(conn_frame, text="端口:").pack(side="left")
        self.port_var = tk.StringVar(value="5900")
        ttk.Entry(conn_frame, textvariable=self.port_var, width=8).pack(side="left", padx=5)
        
        ttk.Label(conn_frame, text="密码:").pack(side="left")
        self.password_var = tk.StringVar()
        ttk.Entry(conn_frame, textvariable=self.password_var, width=10, show="*").pack(side="left", padx=5)
        
        self.connect_btn = ttk.Button(conn_frame, text="连接", command=self.connect_vnc)
        self.connect_btn.pack(side="left", padx=5)
        
        self.disconnect_btn = ttk.Button(conn_frame, text="断开", command=self.disconnect_vnc, state="disabled")
        self.disconnect_btn.pack(side="left", padx=5)
        
        # 状态显示
        status_frame = ttk.Frame(self.root)
        status_frame.pack(fill="x", padx=10, pady=5)
        
        self.status_var = tk.StringVar(value="未连接")
        self.status_label = ttk.Label(status_frame, textvariable=self.status_var)
        self.status_label.pack(side="left")
        
        self.fps_var = tk.StringVar(value="FPS: 0")
        ttk.Label(status_frame, textvariable=self.fps_var).pack(side="right")
        
        # 控制按钮
        ctrl_frame = ttk.Frame(self.root)
        ctrl_frame.pack(fill="x", padx=10, pady=5)
        
        self.live_btn = ttk.Button(ctrl_frame, text="开始实时监控", command=self.toggle_live_capture, state="disabled")
        self.live_btn.pack(side="left", padx=5)
        
        self.screenshot_btn = ttk.Button(ctrl_frame, text="单次截图", command=self.take_screenshot, state="disabled")
        self.screenshot_btn.pack(side="left", padx=5)
        
        # 图像显示区域
        self.image_frame = ttk.Frame(self.root, relief="sunken", borderwidth=2)
        self.image_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        self.image_label = ttk.Label(self.image_frame, text="未连接到VNC服务器", anchor="center")
        self.image_label.pack(fill="both", expand=True)
        
        # 日志区域
        log_frame = ttk.Frame(self.root)
        log_frame.pack(fill="x", padx=10, pady=5)
        
        ttk.Label(log_frame, text="日志:").pack(anchor="w")
        self.log_text = tk.Text(log_frame, height=6)
        self.log_text.pack(fill="x")
        
        # 开始图像更新循环
        self.update_display_image()
        
    def check_dependencies(self):
        """检查依赖库"""
        if not VNCDOTOOL_AVAILABLE:
            self.log_message("错误: vncdotool库未安装")
            messagebox.showerror("依赖缺失", "vncdotool库未安装\n请运行: pip install vncdotool")
        else:
            self.log_message("vncdotool库已就绪")
            
        if not PIL_AVAILABLE:
            self.log_message("错误: Pillow库未安装")
            messagebox.showerror("依赖缺失", "Pillow库未安装\n请运行: pip install Pillow")
        else:
            self.log_message("Pillow库已就绪")
    
    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        
    def connect_vnc(self):
        """连接VNC服务器"""
        if self.connected:
            messagebox.showinfo("提示", "已经连接到VNC服务器")
            return
            
        if not VNCDOTOOL_AVAILABLE:
            messagebox.showerror("错误", "vncdotool库未安装，无法连接")
            return
            
        host = self.host_var.get().strip()
        port_str = self.port_var.get().strip()
        password = self.password_var.get().strip() or None
        
        if not host:
            messagebox.showerror("错误", "请输入服务器地址")
            return
            
        try:
            port = int(port_str)
        except ValueError:
            messagebox.showerror("错误", "端口必须是数字")
            return
            
        # 在后台线程中连接
        self.connect_btn.config(state="disabled")
        self.status_var.set("连接中...")
        
        def connect_thread():
            try:
                self.log_message(f"正在连接到 {host}:{port}...")
                
                # 构建连接字符串
                connection_string = f"{host}::{port}"
                
                # 连接到VNC服务器
                self.vnc_client = api.connect(connection_string, password=password)
                self.connected = True
                
                # 更新UI（在主线程中）
                self.root.after(0, self.on_connect_success)
                
            except Exception as e:
                error_msg = str(e)
                self.root.after(0, lambda: self.on_connect_error(error_msg))
                
        threading.Thread(target=connect_thread, daemon=True).start()
        
    def on_connect_success(self):
        """连接成功的回调"""
        self.status_var.set("已连接")
        self.connect_btn.config(state="disabled")
        self.disconnect_btn.config(state="normal")
        self.live_btn.config(state="normal")
        self.screenshot_btn.config(state="normal")
        self.log_message("VNC连接成功")
        
    def on_connect_error(self, error_msg):
        """连接失败的回调"""
        self.status_var.set("连接失败")
        self.connect_btn.config(state="normal")
        self.log_message(f"连接失败: {error_msg}")
        messagebox.showerror("连接失败", f"无法连接到VNC服务器:\n{error_msg}")
        
    def disconnect_vnc(self):
        """断开VNC连接"""
        if self.live_capture:
            self.stop_live_capture()
            
        self.connected = False
        self.vnc_client = None
        
        self.status_var.set("未连接")
        self.connect_btn.config(state="normal")
        self.disconnect_btn.config(state="disabled")
        self.live_btn.config(state="disabled")
        self.screenshot_btn.config(state="disabled")
        
        self.image_label.config(image="", text="未连接到VNC服务器")
        self.log_message("VNC连接已断开")
        
    def toggle_live_capture(self):
        """切换实时捕获状态"""
        if self.live_capture:
            self.stop_live_capture()
        else:
            self.start_live_capture()
            
    def start_live_capture(self):
        """开始实时捕获"""
        if not self.connected:
            return
            
        self.live_capture = True
        self.frame_count = 0
        self.fps_start_time = time.time()
        
        self.live_btn.config(text="停止实时监控")
        self.log_message("开始实时屏幕监控")
        
        # 启动捕获线程
        self.capture_thread = threading.Thread(target=self.capture_loop, daemon=True)
        self.capture_thread.start()
        
    def stop_live_capture(self):
        """停止实时捕获"""
        self.live_capture = False
        self.live_btn.config(text="开始实时监控")
        self.fps_var.set("FPS: 0")
        self.log_message("停止实时屏幕监控")
        
    def capture_loop(self):
        """实时捕获循环"""
        while self.live_capture and self.connected:
            try:
                # 生成临时文件名
                temp_filename = os.path.join(self.temp_dir, f"frame_{int(time.time() * 1000000)}.png")
                
                # 捕获屏幕
                self.vnc_client.captureScreen(temp_filename)
                
                # 将图像路径放入队列
                if not self.image_queue.full():
                    self.image_queue.put(temp_filename)
                else:
                    # 丢弃最旧的图像
                    try:
                        old_file = self.image_queue.get_nowait()
                        if os.path.exists(old_file):
                            os.remove(old_file)
                    except queue.Empty:
                        pass
                    self.image_queue.put(temp_filename)
                
                # 更新帧计数
                self.frame_count += 1
                
                # 计算FPS
                current_time = time.time()
                if current_time - self.fps_start_time >= 1.0:
                    fps = self.frame_count / (current_time - self.fps_start_time)
                    self.frame_count = 0
                    self.fps_start_time = current_time
                    
                    # 更新FPS显示
                    self.root.after(0, lambda f=fps: self.fps_var.set(f"FPS: {f:.1f}"))
                
                # 控制捕获频率
                time.sleep(0.1)  # 10 FPS
                
            except Exception as e:
                if self.live_capture:
                    self.root.after(0, lambda err=str(e): self.log_message(f"捕获错误: {err}"))
                time.sleep(0.5)
                
    def take_screenshot(self):
        """单次截图"""
        if not self.connected or not self.vnc_client:
            messagebox.showwarning("警告", "请先连接到VNC服务器")
            return
            
        try:
            temp_filename = os.path.join(self.temp_dir, f"screenshot_{int(time.time() * 1000000)}.png")
            self.vnc_client.captureScreen(temp_filename)
            
            # 显示截图
            self.display_image(temp_filename)
            self.log_message("单次截图完成")
            
        except Exception as e:
            error_msg = f"截图失败: {e}"
            self.log_message(error_msg)
            messagebox.showerror("错误", error_msg)
            
    def display_image(self, image_path):
        """显示图像"""
        if not PIL_AVAILABLE or not os.path.exists(image_path):
            return
            
        try:
            image = Image.open(image_path)
            
            # 获取显示区域大小并缩放
            self.image_frame.update_idletasks()
            frame_width = self.image_frame.winfo_width()
            frame_height = self.image_frame.winfo_height()
            
            if frame_width > 1 and frame_height > 1:
                scale_x = (frame_width - 20) / image.width
                scale_y = (frame_height - 20) / image.height
                scale = min(scale_x, scale_y, 1.0)
                
                if scale < 1.0:
                    new_width = int(image.width * scale)
                    new_height = int(image.height * scale)
                    image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            photo = ImageTk.PhotoImage(image)
            self.image_label.config(image=photo, text="")
            self.image_label.image = photo
            
            # 清理临时文件
            try:
                os.remove(image_path)
            except:
                pass
                
        except Exception as e:
            self.log_message(f"显示图像失败: {e}")
            
    def update_display_image(self):
        """更新显示的图像"""
        try:
            if not self.image_queue.empty():
                image_path = self.image_queue.get_nowait()
                self.display_image(image_path)
        except queue.Empty:
            pass
        except Exception as e:
            self.log_message(f"更新显示失败: {e}")
            
        # 继续更新循环
        self.root.after(50, self.update_display_image)  # 20 FPS
        
    def run(self):
        """运行程序"""
        self.root.mainloop()
        
        # 清理临时目录
        try:
            import shutil
            shutil.rmtree(self.temp_dir)
        except:
            pass

if __name__ == "__main__":
    app = SimpleVNCTest()
    app.run()
